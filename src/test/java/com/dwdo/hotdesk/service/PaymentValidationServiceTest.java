package com.dwdo.hotdesk.service;

import com.dwdo.hotdesk.dto.PaymentDataDTO;
import com.dwdo.hotdesk.dto.request.SingleValidRequestDTO;
import com.dwdo.hotdesk.dto.response.GeneralBodyResponse;
import com.dwdo.hotdesk.repository.PaymentSubmissionRepository;
import com.dwdo.hotdesk.restcontrolleradvice.CustomBadRequestException;
import com.dwdo.hotdesk.service.feign.EmployeeClient;
import com.dwdo.hotdesk.service.feign.response.EpiccEmployeeProfileImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

import org.apache.poi.openxml4j.exceptions.NotOfficeXmlFileException;

/**
 * Unit tests for PaymentValidationService
 * Tests Excel file validation and single payment validation functionality
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Payment Validation Service Tests")
class PaymentValidationServiceTest {

    @Mock
    private EmployeeClient employeeClient;

    @Mock
    private PaymentSubmissionRepository submissionRepository;

    @InjectMocks
    private PaymentValidationService paymentValidationService;

    private SingleValidRequestDTO sampleSingleRequest;
    private EpiccEmployeeProfileImpl sampleEmployeeProfile;

    @BeforeEach
    void setUp() {
        // Inject the mocks using reflection since they're @Autowired
        ReflectionTestUtils.setField(paymentValidationService, "employeeClient", employeeClient);
        ReflectionTestUtils.setField(paymentValidationService, "submissionRepository", submissionRepository);

        // Setup sample single request with valid grade and payment type
        sampleSingleRequest = SingleValidRequestDTO.builder()
                .nip("123456789")
                .name("John Doe")
                .grade("U5")
                .paymentType("Bonus Staggered")
                .amount(new BigDecimal("5000000"))
                .description("Monthly salary")
                .monthOfProcess("January")
                .yearOfProcess("2024")
                .build();

        // Setup sample employee profile
        sampleEmployeeProfile = new EpiccEmployeeProfileImpl();
        sampleEmployeeProfile.setDirectorate("IT");
        sampleEmployeeProfile.setTerminationDate(null);
    }

    @Test
    @DisplayName("Should successfully validate single payment with valid data")
    void testValidateSinglePayment_Success() {
        // Given
        when(employeeClient.getProfile("123456789"))
                .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));
        when(submissionRepository.existsByCompositeKey("123456789", "January", "2024", "Bonus Staggered"))
                .thenReturn(false);

        // When
        GeneralBodyResponse response = paymentValidationService.validateSinglePayment(sampleSingleRequest);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("OK", response.getStatus());
        assertEquals("Payment data validated successfully", response.getMessage());

        PaymentDataDTO paymentData = (PaymentDataDTO) response.getData();
        assertNotNull(paymentData);
        assertEquals("123456789", paymentData.getNip());
        assertEquals("John Doe", paymentData.getName());
        assertEquals("U5", paymentData.getGrade());
        assertEquals("Bonus Staggered", paymentData.getPaymentType());
        assertEquals(new BigDecimal("5000000"), paymentData.getAmount());
        assertEquals("Monthly salary", paymentData.getDescription());
        assertEquals("January", paymentData.getMonthOfProcess());
        assertEquals("2024", paymentData.getYearOfProcess());
        assertEquals("NIP is valid", paymentData.getNipValid());
        assertEquals("IT", paymentData.getDirectorate());
        assertTrue(paymentData.getEligible());
        assertEquals("-", paymentData.getSlik());
        assertEquals("-", paymentData.getSanction());
        assertEquals("-", paymentData.getTerminationDate());

        verify(employeeClient, times(2)).getProfile("123456789");
        verify(submissionRepository, times(1)).existsByCompositeKey("123456789", "January", "2024", "Bonus Staggered");
    }

    @Test
    @DisplayName("Should handle single payment validation with invalid NIP")
    void testValidateSinglePayment_InvalidNip() {
        // Given
        when(employeeClient.getProfile("123456789"))
                .thenReturn(null);

        // When
        GeneralBodyResponse response = paymentValidationService.validateSinglePayment(sampleSingleRequest);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("OK", response.getStatus());

        PaymentDataDTO paymentData = (PaymentDataDTO) response.getData();
        assertNotNull(paymentData);
        assertEquals("NIP is invalid", paymentData.getNipValid());
        assertEquals("-", paymentData.getDirectorate());
        assertFalse(paymentData.getEligible());

        verify(employeeClient, times(1)).getProfile("123456789");
    }

    @Test
    @DisplayName("Should handle single payment validation with employee client exception")
    void testValidateSinglePayment_EmployeeClientException() {
        // Given
        when(employeeClient.getProfile("123456789"))
                .thenThrow(new RuntimeException("Service unavailable"));

        // When
        GeneralBodyResponse response = paymentValidationService.validateSinglePayment(sampleSingleRequest);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());

        PaymentDataDTO paymentData = (PaymentDataDTO) response.getData();
        assertNotNull(paymentData);
        assertTrue(paymentData.getNipValid().contains("NIP validation error"));
        assertEquals("-", paymentData.getDirectorate());
        assertFalse(paymentData.getEligible());

        verify(employeeClient, times(1)).getProfile("123456789");
    }

    @Test
    @DisplayName("Should handle single payment validation with missing required fields - NIP")
    void testValidateSinglePayment_MissingNip() {
        // Given
        SingleValidRequestDTO requestWithoutNip = SingleValidRequestDTO.builder()
                .nip(null)
                .name("John Doe")
                .grade("A")
                .paymentType("Salary")
                .amount(new BigDecimal("5000000"))
                .description("Monthly salary")
                .monthOfProcess("January")
                .yearOfProcess("2024")
                .build();

        // When & Then
        CustomBadRequestException exception = assertThrows(CustomBadRequestException.class, () -> {
            paymentValidationService.validateSinglePayment(requestWithoutNip);
        });

        assertEquals(400, exception.getCode());
        assertEquals("Validation Error", exception.getStatus());
        assertEquals("NIP is required", exception.getMessage());

        verify(employeeClient, never()).getProfile(anyString());
    }

    @Test
    @DisplayName("Should handle single payment validation with missing name - fetched from employee client")
    void testValidateSinglePayment_MissingName() {
        // Given
        SingleValidRequestDTO requestWithoutName = SingleValidRequestDTO.builder()
                .nip("123456789")
                .name("")
                .grade("U5")
                .paymentType("Bonus Staggered")
                .amount(new BigDecimal("5000000"))
                .description("Monthly salary")
                .monthOfProcess("January")
                .yearOfProcess("2024")
                .build();

        // Mock employee client response
        EpiccEmployeeProfileImpl mockProfile = new EpiccEmployeeProfileImpl();
        mockProfile.setFirstName("John");
        mockProfile.setLastName("Doe");
        mockProfile.setGrade("U5");
        mockProfile.setDirectorate("IT");
        mockProfile.setTerminationDate(null);

        when(employeeClient.getProfile("123456789"))
                .thenReturn(ResponseEntity.ok(mockProfile));

        // When
        GeneralBodyResponse response = paymentValidationService.validateSinglePayment(requestWithoutName);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());

        PaymentDataDTO paymentData = (PaymentDataDTO) response.getData();
        assertNotNull(paymentData);
        assertEquals("John Doe", paymentData.getName()); // Name fetched from employee client
        assertEquals("123456789", paymentData.getNip());

        verify(employeeClient, times(1)).getProfile("123456789");
    }

    @Test
    @DisplayName("Should handle single payment validation with missing required fields - Amount")
    void testValidateSinglePayment_MissingAmount() {
        // Given
        SingleValidRequestDTO requestWithoutAmount = SingleValidRequestDTO.builder()
                .nip("123456789")
                .name("John Doe")
                .grade("A")
                .paymentType("Salary")
                .amount(null)
                .description("Monthly salary")
                .monthOfProcess("January")
                .yearOfProcess("2024")
                .build();

        // When & Then
        CustomBadRequestException exception = assertThrows(CustomBadRequestException.class, () -> {
            paymentValidationService.validateSinglePayment(requestWithoutAmount);
        });

        assertEquals(400, exception.getCode());
        assertEquals("Validation Error", exception.getStatus());
        assertEquals("Amount is required", exception.getMessage());
    }

    @Test
    @DisplayName("Should normalize month values correctly")
    void testValidateSinglePayment_MonthNormalization() {
        // Given - Request with numeric month
        SingleValidRequestDTO requestWithNumericMonth = SingleValidRequestDTO.builder()
                .nip("123456789")
                .name("John Doe")
                .grade("A")
                .paymentType("Salary")
                .amount(new BigDecimal("5000000"))
                .description("Monthly salary")
                .monthOfProcess("3") // Should be normalized to "March"
                .yearOfProcess("2024")
                .build();

        when(employeeClient.getProfile("123456789"))
                .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));

        // When
        GeneralBodyResponse response = paymentValidationService.validateSinglePayment(requestWithNumericMonth);

        // Then
        PaymentDataDTO paymentData = (PaymentDataDTO) response.getData();
        assertEquals("March", paymentData.getMonthOfProcess());
    }

    @Test
    @DisplayName("Should normalize month abbreviations correctly")
    void testValidateSinglePayment_MonthAbbreviation() {
        // Given - Request with month abbreviation
        SingleValidRequestDTO requestWithAbbreviation = SingleValidRequestDTO.builder()
                .nip("123456789")
                .name("John Doe")
                .grade("A")
                .paymentType("Salary")
                .amount(new BigDecimal("5000000"))
                .description("Monthly salary")
                .monthOfProcess("feb") // Should be normalized to "February"
                .yearOfProcess("2024")
                .build();

        when(employeeClient.getProfile("123456789"))
                .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));

        // When
        GeneralBodyResponse response = paymentValidationService.validateSinglePayment(requestWithAbbreviation);

        // Then
        PaymentDataDTO paymentData = (PaymentDataDTO) response.getData();
        assertEquals("February", paymentData.getMonthOfProcess());
    }

    @Test
    @DisplayName("Should handle employee with termination date")
    void testValidateSinglePayment_WithTerminationDate() {
        // Given
        EpiccEmployeeProfileImpl employeeWithTermination = new EpiccEmployeeProfileImpl();
        employeeWithTermination.setDirectorate("HR");
        employeeWithTermination.setTerminationDate("2024-12-31");

        when(employeeClient.getProfile("123456789"))
                .thenReturn(ResponseEntity.ok(employeeWithTermination));

        // When
        GeneralBodyResponse response = paymentValidationService.validateSinglePayment(sampleSingleRequest);

        // Then
        PaymentDataDTO paymentData = (PaymentDataDTO) response.getData();
        assertEquals("2024-12-31", paymentData.getTerminationDate());
        assertEquals("HR", paymentData.getDirectorate());
    }

    @Test
    @DisplayName("Should reject non-Excel files")
    void testValidateExcelFile_InvalidFileType() {
        // Given
        MockMultipartFile invalidFile = new MockMultipartFile(
                "file",
                "test.txt",
                "text/plain",
                "This is not an Excel file".getBytes()
        );

        // When & Then
        CustomBadRequestException exception = assertThrows(CustomBadRequestException.class, () -> {
            paymentValidationService.validateExcelFile(invalidFile);
        });

        assertEquals(400, exception.getCode());
        assertEquals("Invalid File", exception.getStatus());
        assertEquals("Only Excel files are allowed", exception.getMessage());
    }

    @Test
    @DisplayName("Should handle Excel file processing with invalid content")
    void testValidateExcelFile_InvalidContent() {
        // Given - Create a file that will cause processing exception
        MockMultipartFile corruptedFile = new MockMultipartFile(
                "file",
                "corrupted.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "corrupted content".getBytes()
        );

        // When & Then - The service should throw the actual POI exception since it's not IOException
        assertThrows(NotOfficeXmlFileException.class, () -> {
            paymentValidationService.validateExcelFile(corruptedFile);
        });
    }

    @Test
    @DisplayName("Should accept valid Excel XLS file type but fail on content processing")
    void testValidateExcelFile_ValidXlsFile() {
        // Given
        MockMultipartFile xlsFile = new MockMultipartFile(
                "file",
                "test.xls",
                "application/vnd.ms-excel",
                createMockExcelContent()
        );

        // When & Then - Should pass file type validation but fail on content processing
        assertThrows(NotOfficeXmlFileException.class, () -> {
            paymentValidationService.validateExcelFile(xlsFile);
        });
    }

    @Test
    @DisplayName("Should accept valid Excel XLSX file type but fail on content processing")
    void testValidateExcelFile_ValidXlsxFile() {
        // Given
        MockMultipartFile xlsxFile = new MockMultipartFile(
                "file",
                "test.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                createMockExcelContent()
        );

        // When & Then - Should pass file type validation but fail on content processing
        assertThrows(NotOfficeXmlFileException.class, () -> {
            paymentValidationService.validateExcelFile(xlsxFile);
        });
    }

    @Test
    @DisplayName("Should reject file with null content type")
    void testValidateExcelFile_NullContentType() {
        // Given
        MockMultipartFile fileWithNullContentType = new MockMultipartFile(
                "file",
                "test.xlsx",
                null, // null content type
                "content".getBytes()
        );

        // When & Then
        CustomBadRequestException exception = assertThrows(CustomBadRequestException.class, () -> {
            paymentValidationService.validateExcelFile(fileWithNullContentType);
        });

        assertEquals(400, exception.getCode());
        assertEquals("Invalid File", exception.getStatus());
        assertEquals("Only Excel files are allowed", exception.getMessage());
    }

    @Test
    @DisplayName("Should handle general exception in single payment validation")
    void testValidateSinglePayment_GeneralException() {
        // Given - Create a request that will cause an exception during validation
        SingleValidRequestDTO requestWithInvalidData = SingleValidRequestDTO.builder()
                .nip("123456789")
                .name("John Doe")
                .grade("A")
                .paymentType("Salary")
                .amount(new BigDecimal("5000000"))
                .description("Monthly salary")
                .monthOfProcess("January")
                .yearOfProcess("2024")
                .build();

        // Mock employeeClient to throw an exception that will be caught and wrapped
        when(employeeClient.getProfile("123456789"))
                .thenThrow(new RuntimeException("Database connection failed"));

        // When - The service should handle the exception gracefully and return a response
        GeneralBodyResponse response = paymentValidationService.validateSinglePayment(requestWithInvalidData);

        // Then - Should return a successful response with error details in the payment data
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("OK", response.getStatus());

        PaymentDataDTO paymentData = (PaymentDataDTO) response.getData();
        assertNotNull(paymentData);
        assertTrue(paymentData.getNipValid().contains("NIP validation error"));
        assertTrue(paymentData.getNipValid().contains("Database connection failed"));
        assertFalse(paymentData.getEligible());
    }

    @Test
    @DisplayName("Should handle missing grade - fetched from employee client")
    void testValidateSinglePayment_MissingGrade() {
        // Given
        SingleValidRequestDTO requestWithoutGrade = SingleValidRequestDTO.builder()
                .nip("123456789")
                .name("John Doe")
                .grade("")
                .paymentType("Bonus Staggered")
                .amount(new BigDecimal("5000000"))
                .description("Monthly salary")
                .monthOfProcess("January")
                .yearOfProcess("2024")
                .build();

        // Mock employee client response
        EpiccEmployeeProfileImpl mockProfile = new EpiccEmployeeProfileImpl();
        mockProfile.setFirstName("John");
        mockProfile.setLastName("Doe");
        mockProfile.setGrade("U5");
        mockProfile.setDirectorate("IT");
        mockProfile.setTerminationDate(null);

        when(employeeClient.getProfile("123456789"))
                .thenReturn(ResponseEntity.ok(mockProfile));

        // When
        GeneralBodyResponse response = paymentValidationService.validateSinglePayment(requestWithoutGrade);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());

        PaymentDataDTO paymentData = (PaymentDataDTO) response.getData();
        assertNotNull(paymentData);
        assertEquals("U5", paymentData.getGrade()); // Grade fetched from employee client
        assertEquals("Grade is valid", paymentData.getGradeValid());
        assertEquals("123456789", paymentData.getNip());

        verify(employeeClient, times(1)).getProfile("123456789");
    }

    @Test
    @DisplayName("Should handle missing both name and grade - fetched from employee client")
    void testValidateSinglePayment_MissingNameAndGrade() {
        // Given
        SingleValidRequestDTO requestWithoutNameAndGrade = SingleValidRequestDTO.builder()
                .nip("123456789")
                .name("")
                .grade("")
                .paymentType("Bonus Staggered")
                .amount(new BigDecimal("5000000"))
                .description("Monthly salary")
                .monthOfProcess("January")
                .yearOfProcess("2024")
                .build();

        // Mock employee client response
        EpiccEmployeeProfileImpl mockProfile = new EpiccEmployeeProfileImpl();
        mockProfile.setFirstName("Jane");
        mockProfile.setMiddleName("Marie");
        mockProfile.setLastName("Smith");
        mockProfile.setGrade("U7");
        mockProfile.setDirectorate("HR");
        mockProfile.setTerminationDate(null);

        when(employeeClient.getProfile("123456789"))
                .thenReturn(ResponseEntity.ok(mockProfile));

        // When
        GeneralBodyResponse response = paymentValidationService.validateSinglePayment(requestWithoutNameAndGrade);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());

        PaymentDataDTO paymentData = (PaymentDataDTO) response.getData();
        assertNotNull(paymentData);
        assertEquals("Jane Marie Smith", paymentData.getName()); // Name fetched from employee client
        assertEquals("U7", paymentData.getGrade()); // Grade fetched from employee client
        assertEquals("Grade is valid", paymentData.getGradeValid());
        assertEquals("123456789", paymentData.getNip());

        verify(employeeClient, times(1)).getProfile("123456789");
    }

    @Test
    @DisplayName("Should handle missing required fields - Payment Type")
    void testValidateSinglePayment_MissingPaymentType() {
        // Given
        SingleValidRequestDTO requestWithoutPaymentType = SingleValidRequestDTO.builder()
                .nip("123456789")
                .name("John Doe")
                .grade("A")
                .paymentType(null)
                .amount(new BigDecimal("5000000"))
                .description("Monthly salary")
                .monthOfProcess("January")
                .yearOfProcess("2024")
                .build();

        // When & Then
        CustomBadRequestException exception = assertThrows(CustomBadRequestException.class, () -> {
            paymentValidationService.validateSinglePayment(requestWithoutPaymentType);
        });

        assertEquals(400, exception.getCode());
        assertEquals("Validation Error", exception.getStatus());
        assertEquals("Payment Type is required", exception.getMessage());
    }

    @Test
    @DisplayName("Should handle missing required fields - Description")
    void testValidateSinglePayment_MissingDescription() {
        // Given
        SingleValidRequestDTO requestWithoutDescription = SingleValidRequestDTO.builder()
                .nip("123456789")
                .name("John Doe")
                .grade("A")
                .paymentType("Salary")
                .amount(new BigDecimal("5000000"))
                .description("")
                .monthOfProcess("January")
                .yearOfProcess("2024")
                .build();

        // When & Then
        CustomBadRequestException exception = assertThrows(CustomBadRequestException.class, () -> {
            paymentValidationService.validateSinglePayment(requestWithoutDescription);
        });

        assertEquals(400, exception.getCode());
        assertEquals("Validation Error", exception.getStatus());
        assertEquals("Description is required", exception.getMessage());
    }

    @Test
    @DisplayName("Should handle missing required fields - Month of Process")
    void testValidateSinglePayment_MissingMonthOfProcess() {
        // Given
        SingleValidRequestDTO requestWithoutMonth = SingleValidRequestDTO.builder()
                .nip("123456789")
                .name("John Doe")
                .grade("A")
                .paymentType("Salary")
                .amount(new BigDecimal("5000000"))
                .description("Monthly salary")
                .monthOfProcess(null)
                .yearOfProcess("2024")
                .build();

        // When & Then
        CustomBadRequestException exception = assertThrows(CustomBadRequestException.class, () -> {
            paymentValidationService.validateSinglePayment(requestWithoutMonth);
        });

        assertEquals(400, exception.getCode());
        assertEquals("Validation Error", exception.getStatus());
        assertEquals("Month of Process is required", exception.getMessage());
    }

    @Test
    @DisplayName("Should handle missing required fields - Year of Process")
    void testValidateSinglePayment_MissingYearOfProcess() {
        // Given
        SingleValidRequestDTO requestWithoutYear = SingleValidRequestDTO.builder()
                .nip("123456789")
                .name("John Doe")
                .grade("A")
                .paymentType("Salary")
                .amount(new BigDecimal("5000000"))
                .description("Monthly salary")
                .monthOfProcess("January")
                .yearOfProcess("")
                .build();

        // When & Then
        CustomBadRequestException exception = assertThrows(CustomBadRequestException.class, () -> {
            paymentValidationService.validateSinglePayment(requestWithoutYear);
        });

        assertEquals(400, exception.getCode());
        assertEquals("Validation Error", exception.getStatus());
        assertEquals("Year of Process is required", exception.getMessage());
    }

    @Test
    @DisplayName("Should handle invalid month normalization")
    void testValidateSinglePayment_InvalidMonthNormalization() {
        // Given - Request with invalid month
        SingleValidRequestDTO requestWithInvalidMonth = SingleValidRequestDTO.builder()
                .nip("123456789")
                .name("John Doe")
                .grade("A")
                .paymentType("Salary")
                .amount(new BigDecimal("5000000"))
                .description("Monthly salary")
                .monthOfProcess("InvalidMonth") // Should remain as is
                .yearOfProcess("2024")
                .build();

        when(employeeClient.getProfile("123456789"))
                .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));

        // When
        GeneralBodyResponse response = paymentValidationService.validateSinglePayment(requestWithInvalidMonth);

        // Then
        PaymentDataDTO paymentData = (PaymentDataDTO) response.getData();
        assertEquals("InvalidMonth", paymentData.getMonthOfProcess()); // Should remain unchanged
    }

    @Test
    @DisplayName("Should handle employee response with null body")
    void testValidateSinglePayment_EmployeeResponseNullBody() {
        // Given
        when(employeeClient.getProfile("123456789"))
                .thenReturn(ResponseEntity.ok(null));

        // When
        GeneralBodyResponse response = paymentValidationService.validateSinglePayment(sampleSingleRequest);

        // Then
        PaymentDataDTO paymentData = (PaymentDataDTO) response.getData();
        assertEquals("NIP is invalid", paymentData.getNipValid());
        assertEquals("-", paymentData.getDirectorate());
        assertFalse(paymentData.getEligible());
    }

    @Test
    @DisplayName("Should validate invalid grade")
    void testValidateSinglePayment_InvalidGrade() {
        // Given
        SingleValidRequestDTO requestWithInvalidGrade = SingleValidRequestDTO.builder()
                .nip("123456789")
                .name("John Doe")
                .grade("X1") // Invalid grade
                .paymentType("Bonus Staggered")
                .amount(new BigDecimal("5000000"))
                .description("Monthly salary")
                .monthOfProcess("January")
                .yearOfProcess("2024")
                .build();

        // When & Then
        CustomBadRequestException exception = assertThrows(CustomBadRequestException.class, () -> {
            paymentValidationService.validateSinglePayment(requestWithInvalidGrade);
        });

        assertEquals(400, exception.getCode());
        assertEquals("Validation Error", exception.getStatus());
        assertTrue(exception.getMessage().contains("Invalid grade: X1"));
        assertTrue(exception.getMessage().contains("Valid grades are: U1, U2, U3, U4, U5, U6, U7, U8, U9, U10, U11"));
    }

    @Test
    @DisplayName("Should validate invalid payment type")
    void testValidateSinglePayment_InvalidPaymentType() {
        // Given
        SingleValidRequestDTO requestWithInvalidPaymentType = SingleValidRequestDTO.builder()
                .nip("123456789")
                .name("John Doe")
                .grade("U5")
                .paymentType("Invalid Payment") // Invalid payment type
                .amount(new BigDecimal("5000000"))
                .description("Monthly salary")
                .monthOfProcess("January")
                .yearOfProcess("2024")
                .build();

        // When & Then
        CustomBadRequestException exception = assertThrows(CustomBadRequestException.class, () -> {
            paymentValidationService.validateSinglePayment(requestWithInvalidPaymentType);
        });

        assertEquals(400, exception.getCode());
        assertEquals("Validation Error", exception.getStatus());
        assertTrue(exception.getMessage().contains("Invalid payment type: Invalid Payment"));
        assertTrue(exception.getMessage().contains("Valid payment types are:"));
    }

    @Test
    @DisplayName("Should handle duplicate submission found in database")
    void testValidateSinglePayment_DuplicateSubmission() {
        // Given
        when(employeeClient.getProfile("123456789"))
                .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));
        when(submissionRepository.existsByCompositeKey("123456789", "January", "2024", "Bonus Staggered"))
                .thenReturn(true); // Duplicate found

        // When
        GeneralBodyResponse response = paymentValidationService.validateSinglePayment(sampleSingleRequest);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("OK", response.getStatus());

        PaymentDataDTO paymentData = (PaymentDataDTO) response.getData();
        assertNotNull(paymentData);
        assertFalse(paymentData.getEligible());
        assertEquals("Duplicate submission found for this NIP, payment type, month and year combination",
                     paymentData.getNipValid());
        assertEquals("IT", paymentData.getDirectorate()); // Should still fetch directorate

        verify(employeeClient, times(2)).getProfile("123456789");
        verify(submissionRepository, times(1)).existsByCompositeKey("123456789", "January", "2024", "Bonus Staggered");
    }

    @Test
    @DisplayName("Should validate all valid grades")
    void testValidateSinglePayment_AllValidGrades() {
        // Given
        String[] validGrades = {"U1", "U2", "U3", "U4", "U5", "U6", "U7", "U8", "U9", "U10", "U11"};

        when(employeeClient.getProfile("123456789"))
                .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));
        when(submissionRepository.existsByCompositeKey(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(false);

        for (String grade : validGrades) {
            // When
            SingleValidRequestDTO request = SingleValidRequestDTO.builder()
                    .nip("123456789")
                    .name("John Doe")
                    .grade(grade)
                    .paymentType("Bonus Staggered")
                    .amount(new BigDecimal("5000000"))
                    .description("Monthly salary")
                    .monthOfProcess("January")
                    .yearOfProcess("2024")
                    .build();

            // Then - Should not throw exception
            assertDoesNotThrow(() -> {
                GeneralBodyResponse response = paymentValidationService.validateSinglePayment(request);
                PaymentDataDTO paymentData = (PaymentDataDTO) response.getData();
                assertEquals(grade, paymentData.getGrade());
            });
        }
    }

    @Test
    @DisplayName("Should validate all valid payment types")
    void testValidateSinglePayment_AllValidPaymentTypes() {
        // Given
        String[] validPaymentTypes = {
            "Bonus Staggered", "Retention Bonus", "Token", "Performance Staggered",
            "Salary Adjustment", "Promotion", "Retention Salary"
        };

        when(employeeClient.getProfile("123456789"))
                .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));
        when(submissionRepository.existsByCompositeKey(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(false);

        for (String paymentType : validPaymentTypes) {
            // When
            SingleValidRequestDTO request = SingleValidRequestDTO.builder()
                    .nip("123456789")
                    .name("John Doe")
                    .grade("U5")
                    .paymentType(paymentType)
                    .amount(new BigDecimal("5000000"))
                    .description("Monthly salary")
                    .monthOfProcess("January")
                    .yearOfProcess("2024")
                    .build();

            // Then - Should not throw exception
            assertDoesNotThrow(() -> {
                GeneralBodyResponse response = paymentValidationService.validateSinglePayment(request);
                PaymentDataDTO paymentData = (PaymentDataDTO) response.getData();
                assertEquals(paymentType, paymentData.getPaymentType());
            });
        }
    }

    @Test
    @DisplayName("Should normalize Indonesian month names")
    void testValidateSinglePayment_IndonesianMonthNormalization() {
        // Given
        when(employeeClient.getProfile("123456789"))
                .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));
        when(submissionRepository.existsByCompositeKey(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(false);

        // Test Indonesian month names
        String[][] indonesianMonths = {
            {"januari", "January"},
            {"februari", "February"},
            {"maret", "March"},
            {"mei", "May"},
            {"juni", "June"},
            {"juli", "July"},
            {"agustus", "August"},
            {"oktober", "October"},
            {"desember", "December"}
        };

        for (String[] monthPair : indonesianMonths) {
            SingleValidRequestDTO request = SingleValidRequestDTO.builder()
                    .nip("123456789")
                    .name("John Doe")
                    .grade("U5")
                    .paymentType("Bonus Staggered")
                    .amount(new BigDecimal("5000000"))
                    .description("Monthly salary")
                    .monthOfProcess(monthPair[0]) // Indonesian month
                    .yearOfProcess("2024")
                    .build();

            // When
            GeneralBodyResponse response = paymentValidationService.validateSinglePayment(request);

            // Then
            PaymentDataDTO paymentData = (PaymentDataDTO) response.getData();
            assertEquals(monthPair[1], paymentData.getMonthOfProcess()); // Should be normalized to English
        }
    }

    /**
     * Helper method to create mock Excel content
     * In a real scenario, this would be actual Excel file bytes
     */
    private byte[] createMockExcelContent() {
        // This is a simplified mock - in real tests you might want to create actual Excel content
        // For now, we'll use a simple byte array that represents Excel-like content
        return "Mock Excel Content".getBytes();
    }
}
